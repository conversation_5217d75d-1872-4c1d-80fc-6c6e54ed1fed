import { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ResponsiveContainer, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, Tooltip, BarChart, Bar, XAxis, YAxis, CartesianGrid } from 'recharts';
import apiService from '../../services/apiService';
import EnhancedLoadingScreen from '../UI/EnhancedLoadingScreen';
import useScrollToTop from '../../hooks/useScrollToTop';

const ResultViaIs = () => {
  const { resultId } = useParams();
  const navigate = useNavigate();
  const [result, setResult] = useState(null);
  const [error, setError] = useState('');
  const fetchInProgressRef = useRef(false);
  const abortControllerRef = useRef(null);

  // Scroll to top when component mounts or route changes
  useScrollToTop();

  useEffect(() => {
    // Prevent duplicate calls
    if (fetchInProgressRef.current) {
      return;
    }

    const fetchResult = async (retryCount = 0) => {
      const maxRetries = 5;
      const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 10000); // Exponential backoff, max 10s

      // Create new AbortController for this fetch sequence
      abortControllerRef.current = new AbortController();

      try {
        fetchInProgressRef.current = true;
        const response = await apiService.getResultById(resultId);

        // Check if component is still mounted and request wasn't aborted
        if (!abortControllerRef.current?.signal.aborted) {
          if (response.success) {
            setResult(response.data);
            fetchInProgressRef.current = false;
          }
        }
      } catch (err) {
        // Check if the error is due to abort
        if (abortControllerRef.current?.signal.aborted) {
          return;
        }

        // If it's a 404 and we haven't exceeded max retries, try again
        if (err.response?.status === 404 && retryCount < maxRetries) {
          setTimeout(() => {
            // Check if component is still mounted before retrying
            if (!abortControllerRef.current?.signal.aborted) {
              fetchResult(retryCount + 1);
            }
          }, retryDelay);
        } else {
          // Final error after all retries or non-404 error
          const errorMessage = retryCount >= maxRetries
            ? `Result not found after ${maxRetries + 1} attempts. The analysis may still be processing.`
            : err.response?.data?.message || 'Failed to load results';
          setError(errorMessage);
          fetchInProgressRef.current = false;
        }
      }
    };

    if (resultId) {
      fetchResult();
    } else {
      navigate('/dashboard');
    }

    // Cleanup function
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      fetchInProgressRef.current = false;
    };
  }, [resultId, navigate]);

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // VIA Character Strengths organized by virtue categories
  const viaCategories = {
    wisdom: {
      name: 'Wisdom & Knowledge',
      icon: '◆',
      color: '#1e293b',
      accentColor: '#3b82f6',
      strengths: ['creativity', 'curiosity', 'judgment', 'love_of_learning', 'perspective']
    },
    courage: {
      name: 'Courage',
      icon: '◇',
      color: '#0f172a',
      accentColor: '#ef4444',
      strengths: ['bravery', 'perseverance', 'honesty', 'zest']
    },
    humanity: {
      name: 'Humanity',
      icon: '◈',
      color: '#164e63',
      accentColor: '#06b6d4',
      strengths: ['love', 'kindness', 'social_intelligence']
    },
    justice: {
      name: 'Justice',
      icon: '◉',
      color: '#134e4a',
      accentColor: '#10b981',
      strengths: ['teamwork', 'fairness', 'leadership']
    },
    temperance: {
      name: 'Temperance',
      icon: '◎',
      color: '#7c2d12',
      accentColor: '#f59e0b',
      strengths: ['forgiveness', 'humility', 'prudence', 'self_regulation']
    },
    transcendence: {
      name: 'Transcendence',
      icon: '◐',
      color: '#581c87',
      accentColor: '#a855f7',
      strengths: ['appreciation_of_beauty', 'gratitude', 'hope', 'humor', 'spirituality']
    }
  };

  const strengthLabels = {
    creativity: 'Creativity',
    curiosity: 'Curiosity',
    judgment: 'Critical Thinking',
    love_of_learning: 'Love of Learning',
    perspective: 'Perspective',
    bravery: 'Bravery',
    perseverance: 'Perseverance',
    honesty: 'Honesty',
    zest: 'Zest',
    love: 'Love',
    kindness: 'Kindness',
    social_intelligence: 'Social Intelligence',
    teamwork: 'Teamwork',
    fairness: 'Fairness',
    leadership: 'Leadership',
    forgiveness: 'Forgiveness',
    humility: 'Humility',
    prudence: 'Prudence',
    self_regulation: 'Self-Regulation',
    appreciation_of_beauty: 'Appreciation of Beauty',
    gratitude: 'Gratitude',
    hope: 'Hope',
    humor: 'Humor',
    spirituality: 'Spirituality'
  };

  // VIA Character Strengths detailed data structure
  const viaStrengthsData = {
    'creativity': {
      name: 'Creativity',
      category: 'wisdom',
      description: 'Thinking of novel and productive ways to conceptualize and do things',
      highTraits: [
        'Memiliki kemampuan untuk menghasilkan ide-ide baru dan inovatif',
        'Mampu melihat masalah dari perspektif yang berbeda dan unik',
        'Senang bereksperimen dengan pendekatan baru dalam menyelesaikan tugas',
        'Memiliki imajinasi yang kuat dan kemampuan berpikir di luar kotak',
        'Dapat mengombinasikan ide-ide yang berbeda menjadi solusi kreatif'
      ],
      lowTraits: [
        'Lebih menyukai pendekatan yang sudah terbukti dan konvensional',
        'Merasa lebih nyaman mengikuti prosedur yang telah ditetapkan',
        'Kurang tertarik untuk mencoba metode atau ide yang belum teruji',
        'Lebih fokus pada efisiensi daripada inovasi',
        'Mengutamakan stabilitas dan prediktabilitas dalam pendekatan'
      ],
      implications: {
        high: 'Sangat cocok untuk peran yang membutuhkan inovasi dan pemikiran kreatif, seperti desain, penelitian dan pengembangan, atau bidang seni dan media.',
        low: 'Lebih sesuai untuk peran yang membutuhkan konsistensi dan keandalan, seperti operasional, administrasi, atau implementasi sistem yang sudah ada.'
      }
    },
    'curiosity': {
      name: 'Curiosity',
      category: 'wisdom',
      description: 'Taking an interest in ongoing experience for its own sake',
      highTraits: [
        'Memiliki rasa ingin tahu yang tinggi terhadap berbagai topik dan pengalaman',
        'Senang mengeksplorasi ide-ide baru dan mempelajari hal-hal yang belum diketahui',
        'Aktif mencari informasi dan pengetahuan dari berbagai sumber',
        'Terbuka terhadap pengalaman baru dan tantangan intelektual',
        'Memiliki motivasi intrinsik untuk terus belajar dan berkembang'
      ],
      lowTraits: [
        'Lebih fokus pada area keahlian yang sudah dikuasai',
        'Merasa cukup dengan pengetahuan yang sudah dimiliki',
        'Kurang tertarik untuk mengeksplorasi topik di luar bidang utama',
        'Lebih menyukai rutinitas dan hal-hal yang sudah familiar',
        'Mengutamakan pendalaman daripada perluasan pengetahuan'
      ],
      implications: {
        high: 'Ideal untuk peran yang melibatkan penelitian, pembelajaran berkelanjutan, atau eksplorasi bidang baru seperti R&D, jurnalisme, atau konsultasi.',
        low: 'Lebih cocok untuk peran yang membutuhkan keahlian mendalam di bidang spesifik dan konsistensi dalam penerapan pengetahuan yang sudah ada.'
      }
    },
    'judgment': {
      name: 'Critical Thinking',
      category: 'wisdom',
      description: 'Thinking things through and examining them from all sides',
      highTraits: [
        'Mampu menganalisis informasi secara objektif dan menyeluruh',
        'Memiliki kemampuan untuk mengevaluasi argumen dan bukti dengan kritis',
        'Dapat mengidentifikasi bias dan kelemahan dalam pemikiran',
        'Senang mempertimbangkan berbagai perspektif sebelum mengambil keputusan',
        'Memiliki standar tinggi untuk kualitas pemikiran dan reasoning'
      ],
      lowTraits: [
        'Lebih mengandalkan intuisi dan perasaan dalam pengambilan keputusan',
        'Cenderung menerima informasi tanpa analisis mendalam',
        'Kurang tertarik pada detail dan nuansa dalam argumen',
        'Lebih menyukai keputusan yang cepat daripada analisis yang panjang',
        'Mengutamakan harmoni daripada kritik konstruktif'
      ],
      implications: {
        high: 'Sangat sesuai untuk peran yang membutuhkan analisis mendalam dan pengambilan keputusan strategis, seperti manajemen senior, audit, atau konsultasi strategis.',
        low: 'Lebih cocok untuk peran yang membutuhkan empati dan hubungan interpersonal yang kuat, atau pekerjaan yang bersifat eksekutif dengan panduan yang jelas.'
      }
    },
    'love_of_learning': {
      name: 'Love of Learning',
      category: 'wisdom',
      description: 'Mastering new skills, topics, and bodies of knowledge',
      highTraits: [
        'Memiliki motivasi tinggi untuk terus mengembangkan pengetahuan dan keterampilan',
        'Senang mengikuti pelatihan, kursus, atau program pengembangan diri',
        'Aktif mencari peluang untuk mempelajari hal-hal baru di bidang pekerjaan',
        'Memiliki kemampuan untuk belajar secara mandiri dan berkelanjutan',
        'Merasa energik dan termotivasi ketika menghadapi tantangan pembelajaran baru'
      ],
      lowTraits: [
        'Lebih fokus pada penerapan pengetahuan yang sudah dimiliki',
        'Merasa cukup dengan tingkat keahlian saat ini',
        'Kurang tertarik pada program pengembangan atau pelatihan tambahan',
        'Lebih menyukai stabilitas dalam rutinitas kerja',
        'Mengutamakan efisiensi dalam tugas yang sudah dikuasai'
      ],
      implications: {
        high: 'Ideal untuk lingkungan kerja yang dinamis dan berkembang pesat, seperti teknologi, akademik, atau bidang yang membutuhkan adaptasi berkelanjutan.',
        low: 'Lebih sesuai untuk peran yang membutuhkan keahlian stabil dan konsisten, seperti operasional rutin atau spesialisasi teknis yang sudah mapan.'
      }
    },
    'perspective': {
      name: 'Perspective',
      category: 'wisdom',
      description: 'Being able to provide wise counsel; having ways of looking at the world',
      highTraits: [
        'Mampu memberikan nasihat yang bijaksana dan perspektif yang luas',
        'Memiliki kemampuan untuk melihat gambaran besar dan konteks yang lebih luas',
        'Dapat membantu orang lain memahami situasi dari berbagai sudut pandang',
        'Memiliki pengalaman hidup yang kaya dan dapat mengambil pelajaran darinya',
        'Dipercaya sebagai sumber kebijaksaan dan panduan oleh orang lain'
      ],
      lowTraits: [
        'Lebih fokus pada detail dan aspek teknis daripada gambaran besar',
        'Kurang nyaman memberikan nasihat atau panduan kepada orang lain',
        'Lebih menyukai peran eksekutif daripada advisory',
        'Mengutamakan tindakan praktis daripada refleksi filosofis',
        'Lebih tertarik pada solusi konkret daripada pemahaman konseptual'
      ],
      implications: {
        high: 'Sangat cocok untuk peran kepemimpinan, mentoring, atau konsultasi yang membutuhkan kebijaksaan dan pandangan strategis jangka panjang.',
        low: 'Lebih sesuai untuk peran teknis atau operasional yang membutuhkan fokus pada detail dan implementasi praktis.'
      }
    },
    'bravery': {
      name: 'Bravery',
      category: 'courage',
      description: 'Not shrinking from threat, challenge, difficulty, or pain',
      highTraits: [
        'Berani menghadapi tantangan dan situasi yang sulit atau berisiko',
        'Mampu mengambil keputusan sulit meskipun ada tekanan atau ketidakpastian',
        'Tidak mudah mundur ketika menghadapi hambatan atau kritik',
        'Bersedia membela prinsip dan nilai-nilai meskipun tidak populer',
        'Memiliki ketahanan mental yang kuat dalam menghadapi adversitas'
      ],
      lowTraits: [
        'Lebih menyukai lingkungan kerja yang stabil dan dapat diprediksi',
        'Cenderung menghindari konflik atau situasi yang kontroversial',
        'Lebih nyaman dengan tugas-tugas yang sudah familiar dan aman',
        'Mengutamakan konsensus dan harmoni daripada mengambil posisi yang berani',
        'Lebih suka bekerja dalam tim daripada mengambil tanggung jawab individual yang besar'
      ],
      implications: {
        high: 'Ideal untuk peran kepemimpinan dalam situasi krisis, change management, atau bidang yang membutuhkan pengambilan risiko strategis.',
        low: 'Lebih cocok untuk peran yang membutuhkan stabilitas, kerja sama tim, dan lingkungan kerja yang harmonis dan mendukung.'
      }
    },
    'perseverance': {
      name: 'Perseverance',
      category: 'courage',
      description: 'Finishing what one begins; persisting in a course of action despite obstacles',
      highTraits: [
        'Memiliki kemampuan untuk menyelesaikan tugas hingga tuntas meskipun menghadapi hambatan',
        'Tidak mudah menyerah ketika menghadapi kesulitan atau kegagalan',
        'Mampu mempertahankan motivasi dan fokus dalam jangka panjang',
        'Memiliki disiplin diri yang kuat untuk terus bekerja menuju tujuan',
        'Dapat bangkit kembali setelah mengalami kemunduran atau kegagalan'
      ],
      lowTraits: [
        'Cenderung beralih ke tugas lain ketika menghadapi hambatan',
        'Lebih menyukai proyek dengan hasil yang cepat terlihat',
        'Kurang sabar dengan proses yang membutuhkan waktu lama',
        'Mudah kehilangan motivasi ketika tidak melihat progress yang jelas',
        'Lebih tertarik pada variasi daripada konsistensi jangka panjang'
      ],
      implications: {
        high: 'Sangat cocok untuk proyek jangka panjang, penelitian, atau bidang yang membutuhkan konsistensi dan ketekunan tinggi.',
        low: 'Lebih sesuai untuk peran yang dinamis dengan variasi tugas dan hasil yang cepat terlihat.'
      }
    },
    'honesty': {
      name: 'Honesty',
      category: 'courage',
      description: 'Speaking the truth and presenting oneself in a genuine way',
      highTraits: [
        'Selalu berusaha untuk jujur dan transparan dalam komunikasi',
        'Memiliki integritas yang tinggi dan konsisten antara kata dan tindakan',
        'Berani menyampaikan kebenaran meskipun tidak populer',
        'Dapat dipercaya untuk memberikan informasi yang akurat',
        'Menunjukkan keaslian diri tanpa berpura-pura atau menyembunyikan kepribadian'
      ],
      lowTraits: [
        'Kadang menyesuaikan komunikasi untuk menghindari konflik',
        'Lebih mempertimbangkan dampak sosial daripada kebenaran mutlak',
        'Cenderung diplomatik dalam menyampaikan informasi sensitif',
        'Mengutamakan harmoni hubungan daripada transparansi total',
        'Lebih fleksibel dalam presentasi diri sesuai situasi'
      ],
      implications: {
        high: 'Ideal untuk peran yang membutuhkan kepercayaan tinggi seperti audit, konsultasi, atau posisi kepemimpinan yang membutuhkan integritas.',
        low: 'Lebih cocok untuk peran yang membutuhkan diplomasi dan kemampuan adaptasi sosial yang tinggi.'
      }
    },
    'zest': {
      name: 'Zest',
      category: 'courage',
      description: 'Approaching life with excitement and energy',
      highTraits: [
        'Memiliki energi dan antusiasme yang tinggi dalam menghadapi tugas',
        'Mampu memotivasi dan menginspirasi orang lain dengan semangat',
        'Menunjukkan passion yang kuat terhadap pekerjaan dan aktivitas',
        'Memiliki vitalitas yang dapat dipertahankan dalam jangka panjang',
        'Melihat peluang dan tantangan dengan optimisme dan kegembiraan'
      ],
      lowTraits: [
        'Lebih tenang dan stabil dalam pendekatan terhadap tugas',
        'Mengutamakan efisiensi daripada antusiasme dalam bekerja',
        'Lebih suka bekerja dengan ritme yang konsisten dan terkendali',
        'Kurang menunjukkan emosi atau excitement secara eksternal',
        'Lebih fokus pada hasil daripada proses yang energik'
      ],
      implications: {
        high: 'Sangat cocok untuk peran yang membutuhkan motivasi tim, sales, atau bidang kreatif yang membutuhkan energi tinggi.',
        low: 'Lebih sesuai untuk peran analitis, teknis, atau yang membutuhkan konsistensi dan stabilitas.'
      }
    },
    'love': {
      name: 'Love',
      category: 'humanity',
      description: 'Capacity for close relationships; valuing close relations with others',
      highTraits: [
        'Mampu membentuk dan mempertahankan hubungan yang mendalam dan bermakna',
        'Menunjukkan kasih sayang dan perhatian yang tulus kepada orang lain',
        'Memiliki kemampuan empati yang tinggi dan dapat memahami perasaan orang lain',
        'Bersedia menginvestasikan waktu dan energi untuk hubungan personal',
        'Menciptakan lingkungan yang hangat dan mendukung bagi orang-orang terdekat'
      ],
      lowTraits: [
        'Lebih fokus pada tugas dan pencapaian daripada hubungan personal',
        'Cenderung menjaga jarak emosional dalam interaksi profesional',
        'Lebih nyaman dengan hubungan yang formal dan terstruktur',
        'Mengutamakan efisiensi daripada koneksi emosional',
        'Lebih suka bekerja secara independen daripada dalam tim yang erat'
      ],
      implications: {
        high: 'Ideal untuk peran yang melibatkan mentoring, counseling, HR, atau kepemimpinan yang membutuhkan koneksi personal yang kuat.',
        low: 'Lebih cocok untuk peran teknis, analitis, atau yang membutuhkan objektivitas dan fokus pada hasil.'
      }
    },
    'kindness': {
      name: 'Kindness',
      category: 'humanity',
      description: 'Doing favors and good deeds for others; helping them; taking care of them',
      highTraits: [
        'Selalu siap membantu orang lain tanpa mengharapkan imbalan',
        'Menunjukkan kepedulian dan perhatian terhadap kesejahteraan rekan kerja',
        'Memiliki kemampuan untuk melihat kebutuhan orang lain dan bertindak untuk membantu',
        'Menciptakan lingkungan kerja yang supportive dan inclusive',
        'Bersedia mengorbankan waktu dan sumber daya untuk membantu orang lain'
      ],
      lowTraits: [
        'Lebih fokus pada tanggung jawab dan tugas pribadi',
        'Mengutamakan efisiensi dan produktivitas daripada membantu orang lain',
        'Cenderung menunggu diminta sebelum menawarkan bantuan',
        'Lebih objektif dalam menilai situasi tanpa terlalu terlibat emosional',
        'Mengharapkan orang lain untuk mandiri dan menyelesaikan masalah sendiri'
      ],
      implications: {
        high: 'Sangat cocok untuk peran dalam customer service, healthcare, education, atau posisi yang membutuhkan pelayanan dan dukungan kepada orang lain.',
        low: 'Lebih sesuai untuk peran yang membutuhkan objektivitas, analisis, atau fokus pada pencapaian target individual.'
      }
    },
    'social_intelligence': {
      name: 'Social Intelligence',
      category: 'humanity',
      description: 'Understanding the motives and feelings of others and oneself',
      highTraits: [
        'Mampu membaca dan memahami dinamika sosial dengan akurat',
        'Memiliki kemampuan untuk menyesuaikan komunikasi sesuai dengan audiens',
        'Dapat mengidentifikasi motivasi dan emosi orang lain dengan tepat',
        'Mahir dalam navigasi politik organisasi dan hubungan interpersonal',
        'Memiliki awareness yang tinggi terhadap dampak perilaku sendiri pada orang lain'
      ],
      lowTraits: [
        'Lebih fokus pada konten daripada konteks sosial dalam komunikasi',
        'Cenderung straightforward dan direct dalam interaksi',
        'Kurang memperhatikan nuansa emosional dalam situasi sosial',
        'Lebih nyaman dengan komunikasi yang eksplisit dan jelas',
        'Mengutamakan substansi daripada diplomasi dalam berinteraksi'
      ],
      implications: {
        high: 'Ideal untuk peran dalam sales, negotiation, public relations, atau kepemimpinan yang membutuhkan kemampuan interpersonal yang tinggi.',
        low: 'Lebih cocok untuk peran teknis, penelitian, atau yang membutuhkan fokus pada detail dan akurasi daripada dinamika sosial.'
      }
    },
    'teamwork': {
      name: 'Teamwork',
      category: 'justice',
      description: 'Citizenship, social responsibility, loyalty, teamwork',
      highTraits: [
        'Memiliki komitmen yang kuat terhadap kesuksesan tim dan organisasi',
        'Bersedia mengorbankan kepentingan pribadi untuk kepentingan bersama',
        'Menunjukkan loyalitas dan dedikasi yang tinggi terhadap kelompok',
        'Aktif berkontribusi dalam menciptakan lingkungan kerja yang kolaboratif',
        'Memiliki sense of responsibility yang kuat terhadap komunitas dan organisasi'
      ],
      lowTraits: [
        'Lebih fokus pada pencapaian individual daripada tim',
        'Cenderung bekerja secara independen dan mandiri',
        'Mengutamakan efisiensi personal daripada proses kolaboratif',
        'Kurang tertarik pada aktivitas team building atau sosial',
        'Lebih objektif dalam menilai kontribusi tanpa bias kelompok'
      ],
      implications: {
        high: 'Sangat cocok untuk peran yang membutuhkan kolaborasi tinggi, project management, atau posisi yang membutuhkan team cohesion yang kuat.',
        low: 'Lebih sesuai untuk peran individual contributor, research, atau yang membutuhkan fokus dan konsentrasi tinggi.'
      }
    },
    'fairness': {
      name: 'Fairness',
      category: 'justice',
      description: 'Treating all people the same according to notions of fairness and justice',
      highTraits: [
        'Selalu berusaha untuk berlaku adil dan objektif dalam setiap situasi',
        'Memiliki prinsip yang kuat tentang kesetaraan dan keadilan',
        'Mampu membuat keputusan yang tidak bias dan berdasarkan merit',
        'Bersedia membela orang yang diperlakukan tidak adil',
        'Konsisten dalam menerapkan standar dan aturan untuk semua orang'
      ],
      lowTraits: [
        'Lebih fleksibel dalam menerapkan aturan sesuai konteks',
        'Cenderung mempertimbangkan faktor personal dalam pengambilan keputusan',
        'Lebih pragmatis daripada idealis dalam pendekatan terhadap keadilan',
        'Mengutamakan hasil daripada proses yang sempurna',
        'Lebih toleran terhadap perbedaan perlakuan jika ada alasan yang valid'
      ],
      implications: {
        high: 'Ideal untuk peran dalam HR, legal, audit, atau posisi kepemimpinan yang membutuhkan pengambilan keputusan yang adil dan objektif.',
        low: 'Lebih cocok untuk peran yang membutuhkan fleksibilitas, adaptasi, atau pendekatan yang lebih personal dan kontekstual.'
      }
    },
    'leadership': {
      name: 'Leadership',
      category: 'justice',
      description: 'Encouraging a group of which one is a member to get things done',
      highTraits: [
        'Memiliki kemampuan natural untuk mempengaruhi dan menginspirasi orang lain',
        'Berani mengambil tanggung jawab dan membuat keputusan sulit',
        'Mampu mengorganisir dan mengarahkan tim menuju tujuan bersama',
        'Memiliki visi yang jelas dan dapat mengkomunikasikannya dengan efektif',
        'Bersedia mengambil inisiatif dan memimpin perubahan'
      ],
      lowTraits: [
        'Lebih nyaman sebagai individual contributor atau follower',
        'Cenderung menghindari tanggung jawab pengambilan keputusan untuk orang lain',
        'Lebih suka bekerja dengan panduan yang jelas daripada membuat arah sendiri',
        'Kurang tertarik pada aspek politik dan dinamika kekuasaan',
        'Mengutamakan keahlian teknis daripada kemampuan manajerial'
      ],
      implications: {
        high: 'Sangat cocok untuk posisi managerial, executive, atau peran yang membutuhkan kemampuan untuk memimpin dan mengarahkan orang lain.',
        low: 'Lebih sesuai untuk peran specialist, technical expert, atau posisi yang membutuhkan keahlian mendalam tanpa tanggung jawab manajerial.'
      }
    },
    'forgiveness': {
      name: 'Forgiveness',
      category: 'temperance',
      description: 'Forgiving those who have done wrong; second chances; mercy',
      highTraits: [
        'Mampu melepaskan dendam dan memberikan kesempatan kedua kepada orang lain',
        'Memiliki kemampuan untuk memisahkan tindakan dari pribadi seseorang',
        'Bersedia melupakan kesalahan masa lalu dan fokus pada masa depan',
        'Menunjukkan compassion dan understanding terhadap kelemahan manusia',
        'Dapat mempertahankan hubungan meskipun pernah mengalami konflik'
      ],
      lowTraits: [
        'Lebih sulit untuk melupakan kesalahan atau pelanggaran',
        'Cenderung menjaga jarak dengan orang yang pernah mengecewakan',
        'Mengutamakan akuntabilitas dan konsekuensi atas tindakan',
        'Lebih protektif terhadap diri sendiri dan orang-orang terdekat',
        'Memiliki standar tinggi untuk kepercayaan dan integritas'
      ],
      implications: {
        high: 'Ideal untuk peran dalam conflict resolution, counseling, HR, atau posisi yang membutuhkan kemampuan untuk mengelola hubungan yang kompleks.',
        low: 'Lebih cocok untuk peran yang membutuhkan standar tinggi, quality control, atau posisi yang membutuhkan konsistensi dan akuntabilitas.'
      }
    },
    'humility': {
      name: 'Humility',
      category: 'temperance',
      description: 'Modesty; letting accomplishments speak for themselves',
      highTraits: [
        'Tidak sombong atau membanggakan pencapaian secara berlebihan',
        'Bersedia mengakui kesalahan dan belajar dari orang lain',
        'Memiliki perspektif yang realistis tentang kemampuan dan keterbatasan diri',
        'Menghargai kontribusi orang lain dan memberikan credit yang pantas',
        'Lebih fokus pada pembelajaran dan improvement daripada pengakuan'
      ],
      lowTraits: [
        'Lebih percaya diri dalam mempromosikan kemampuan dan pencapaian',
        'Cenderung mengambil credit untuk kesuksesan dan pencapaian',
        'Memiliki confidence yang tinggi dalam kemampuan dan judgment',
        'Kurang ragu untuk memimpin atau mengambil posisi dominan',
        'Lebih assertive dalam menyampaikan pendapat dan ide'
      ],
      implications: {
        high: 'Sangat cocok untuk peran yang membutuhkan pembelajaran berkelanjutan, research, atau posisi yang membutuhkan kolaborasi dan team harmony.',
        low: 'Lebih sesuai untuk peran leadership, sales, atau posisi yang membutuhkan confidence dan kemampuan untuk mempromosikan ide atau produk.'
      }
    },
    'prudence': {
      name: 'Prudence',
      category: 'temperance',
      description: 'Careful choices; not taking undue risks; not saying or doing things that might later be regretted',
      highTraits: [
        'Selalu mempertimbangkan konsekuensi sebelum mengambil keputusan',
        'Memiliki kemampuan untuk menahan diri dari tindakan impulsif',
        'Berhati-hati dalam komunikasi dan menghindari pernyataan yang merugikan',
        'Mengutamakan perencanaan dan persiapan yang matang',
        'Mampu mengelola risiko dengan bijaksana dan calculated'
      ],
      lowTraits: [
        'Lebih spontan dan fleksibel dalam pengambilan keputusan',
        'Cenderung mengambil risiko untuk mendapatkan hasil yang lebih besar',
        'Lebih direct dan straightforward dalam komunikasi',
        'Kurang sabar dengan proses perencanaan yang terlalu detail',
        'Lebih tertarik pada action daripada analysis yang berkepanjangan'
      ],
      implications: {
        high: 'Ideal untuk peran dalam risk management, financial planning, legal, atau posisi yang membutuhkan careful decision making.',
        low: 'Lebih cocok untuk peran yang membutuhkan quick decision making, innovation, atau lingkungan yang dinamis dan fast-paced.'
      }
    },
    'self_regulation': {
      name: 'Self-Regulation',
      category: 'temperance',
      description: 'Regulating what one feels and does; being disciplined; controlling appetites and emotions',
      highTraits: [
        'Memiliki kontrol diri yang kuat terhadap emosi dan impuls',
        'Mampu mempertahankan fokus dan disiplin dalam jangka panjang',
        'Bersedia menunda gratifikasi untuk mencapai tujuan yang lebih besar',
        'Dapat mengelola stress dan tekanan dengan tenang dan terkendali',
        'Konsisten dalam menjalankan rutinitas dan komitmen yang telah dibuat'
      ],
      lowTraits: [
        'Lebih ekspresif dan spontan dalam menunjukkan emosi',
        'Cenderung mengikuti mood dan energy level yang berfluktuasi',
        'Lebih fleksibel dalam mengubah rencana sesuai situasi',
        'Kurang sabar dengan rutinitas yang monoton atau rigid',
        'Lebih responsif terhadap stimulus eksternal dan perubahan lingkungan'
      ],
      implications: {
        high: 'Sangat cocok untuk peran yang membutuhkan consistency, long-term projects, atau posisi yang membutuhkan emotional stability.',
        low: 'Lebih sesuai untuk peran kreatif, dynamic environment, atau posisi yang membutuhkan adaptability dan responsiveness.'
      }
    },
    'appreciation_of_beauty': {
      name: 'Appreciation of Beauty',
      category: 'transcendence',
      description: 'Noticing and appreciating beauty, excellence, and skilled performance in various domains',
      highTraits: [
        'Memiliki kepekaan tinggi terhadap keindahan dalam berbagai bentuk',
        'Dapat menghargai excellence dan craftsmanship dalam pekerjaan',
        'Termotivasi oleh aesthetic dan kualitas dalam lingkungan kerja',
        'Memiliki kemampuan untuk melihat dan menciptakan harmony dalam design',
        'Menghargai detail dan finesse dalam hasil kerja'
      ],
      lowTraits: [
        'Lebih fokus pada fungsi daripada form dalam pendekatan kerja',
        'Mengutamakan efisiensi dan praktikalitas daripada aesthetic',
        'Kurang memperhatikan aspek visual atau artistic dalam pekerjaan',
        'Lebih tertarik pada substance daripada presentation',
        'Cenderung pragmatis dalam menilai kualitas berdasarkan utility'
      ],
      implications: {
        high: 'Ideal untuk peran dalam design, arts, architecture, atau bidang yang membutuhkan aesthetic sense dan appreciation untuk quality.',
        low: 'Lebih cocok untuk peran teknis, engineering, atau yang membutuhkan fokus pada functionality dan efficiency.'
      }
    },
    'gratitude': {
      name: 'Gratitude',
      category: 'transcendence',
      description: 'Being aware of and thankful for good things that happen',
      highTraits: [
        'Selalu menghargai dan berterima kasih atas dukungan yang diterima',
        'Memiliki perspektif positif dan dapat melihat sisi baik dalam situasi',
        'Aktif mengekspresikan appreciation kepada rekan kerja dan atasan',
        'Mampu mempertahankan motivasi dengan menghargai pencapaian kecil',
        'Menciptakan lingkungan kerja yang positif dengan sikap appreciative'
      ],
      lowTraits: [
        'Lebih fokus pada area yang perlu diperbaiki daripada yang sudah baik',
        'Cenderung melihat dukungan sebagai hal yang wajar atau expected',
        'Kurang ekspresif dalam menunjukkan appreciation',
        'Lebih critical dan analytical dalam mengevaluasi situasi',
        'Mengutamakan improvement daripada celebration atas pencapaian'
      ],
      implications: {
        high: 'Sangat cocok untuk peran dalam team leadership, customer relations, atau posisi yang membutuhkan positive team dynamics.',
        low: 'Lebih sesuai untuk peran dalam quality assurance, analysis, atau posisi yang membutuhkan critical thinking dan improvement focus.'
      }
    },
    'hope': {
      name: 'Hope',
      category: 'transcendence',
      description: 'Optimism, future-mindedness, future orientation',
      highTraits: [
        'Memiliki optimisme yang kuat tentang masa depan dan kemungkinan',
        'Mampu mempertahankan motivasi meskipun menghadapi kemunduran',
        'Berfokus pada peluang dan potensi daripada hambatan',
        'Memiliki visi yang jelas tentang tujuan jangka panjang',
        'Dapat menginspirasi orang lain dengan perspektif positif tentang masa depan'
      ],
      lowTraits: [
        'Lebih realistis dan cautious dalam memandang masa depan',
        'Cenderung fokus pada situasi present daripada planning jangka panjang',
        'Lebih skeptical terhadap perubahan atau improvement yang besar',
        'Mengutamakan stability dan predictability daripada growth potential',
        'Lebih praktis dalam menilai kemungkinan dan risiko'
      ],
      implications: {
        high: 'Ideal untuk peran dalam strategic planning, change management, atau posisi yang membutuhkan vision dan future orientation.',
        low: 'Lebih cocok untuk peran dalam operations, maintenance, atau posisi yang membutuhkan focus pada current state dan stability.'
      }
    },
    'humor': {
      name: 'Humor',
      category: 'transcendence',
      description: 'Liking to laugh and tease; bringing smiles to other people; seeing the light side',
      highTraits: [
        'Mampu menggunakan humor untuk mencairkan suasana dan mengurangi tension',
        'Memiliki kemampuan untuk melihat sisi lucu dalam situasi sulit',
        'Dapat menciptakan lingkungan kerja yang enjoyable dan relaxed',
        'Menggunakan humor sebagai tool untuk building rapport dan connection',
        'Memiliki perspective yang light-hearted terhadap challenges'
      ],
      lowTraits: [
        'Lebih serius dan formal dalam pendekatan terhadap pekerjaan',
        'Cenderung menjaga profesionalitas dengan menghindari humor',
        'Lebih fokus pada task completion daripada team enjoyment',
        'Kurang comfortable dengan banter atau casual interaction',
        'Mengutamakan efficiency daripada creating positive atmosphere'
      ],
      implications: {
        high: 'Sangat cocok untuk peran dalam team building, customer service, atau posisi yang membutuhkan interpersonal skills dan team morale.',
        low: 'Lebih sesuai untuk peran formal, technical, atau yang membutuhkan serious focus dan professional demeanor.'
      }
    },
    'spirituality': {
      name: 'Spirituality',
      category: 'transcendence',
      description: 'Having coherent beliefs about the higher purpose and meaning of life',
      highTraits: [
        'Memiliki sense of purpose yang kuat dan meaning dalam pekerjaan',
        'Mampu melihat kontribusi pekerjaan dalam konteks yang lebih besar',
        'Termotivasi oleh values dan principles yang mendalam',
        'Memiliki perspective yang holistic tentang life dan career',
        'Dapat menemukan fulfillment melalui service dan contribution'
      ],
      lowTraits: [
        'Lebih fokus pada aspek praktis dan tangible dari pekerjaan',
        'Cenderung memisahkan personal values dari professional responsibilities',
        'Mengutamakan achievement dan recognition daripada meaning',
        'Lebih comfortable dengan goals yang specific dan measurable',
        'Fokus pada immediate results daripada long-term impact'
      ],
      implications: {
        high: 'Ideal untuk peran dalam non-profit, education, healthcare, atau posisi yang memiliki clear social impact dan purpose.',
        low: 'Lebih cocok untuk peran dalam business, finance, atau yang membutuhkan focus pada measurable outcomes dan practical results.'
      }
    }
  };

  const getStrengthDescription = (strength) => {
    return viaStrengthsData[strength]?.description || 'A valuable character strength';
  };

  // VIA explanation data
  const viaExplanation = {
    title: "VIA Character Strengths Survey",
    description: "VIA (Values in Action) Survey mengidentifikasi kekuatan karakter yang merupakan inti dari kepribadian positif. Survey ini mengukur 24 kekuatan karakter yang diorganisir ke dalam 6 kategori kebajikan universal.",
    developer: "Dikembangkan oleh Dr. Christopher Peterson dan Dr. Martin Seligman (2004) berdasarkan penelitian lintas budaya",
    validity: "Telah divalidasi secara ilmiah di lebih dari 190 negara dengan jutaan responden. Instrumen ini digunakan secara luas dalam psikologi positif dan pengembangan karakter.",
    purpose: "Mengidentifikasi kekuatan karakter signature Anda yang dapat digunakan untuk meningkatkan kesejahteraan, kinerja, dan kepuasan hidup.",
    dimensions: Object.entries(viaCategories).map(([key, category]) => ({
      key,
      name: category.name,
      description: `${category.strengths.length} kekuatan karakter`,
      icon: category.icon
    }))
  };

  const getViaIsInsights = (viaIsData) => {
    if (!viaIsData) return { top: [], bottom: [], byCategory: {} };

    const entries = Object.entries(viaIsData).sort(([,a], [,b]) => b - a);

    // Group by categories
    const byCategory = {};
    Object.entries(viaCategories).forEach(([categoryKey, category]) => {
      byCategory[categoryKey] = entries
        .filter(([strength]) => category.strengths.includes(strength))
        .map(([strength, score]) => ({ strength, score, label: strengthLabels[strength] }));
    });

    return {
      top: entries.slice(0, 5),
      bottom: entries.slice(-5),
      byCategory
    };
  };

  const getScoreLevel = (score) => {
    if (score >= 4.0) return {
      level: 'Signature Strength',
      intensity: 'text-emerald-700 font-semibold',
      bg: 'bg-emerald-50 border-emerald-200',
      description: 'Skor 4.0-5.0: Kekuatan karakter utama yang sangat menonjol',
      color: '#059669'
    };
    if (score >= 3.5) return {
      level: 'High Strength',
      intensity: 'text-blue-700 font-semibold',
      bg: 'bg-blue-50 border-blue-200',
      description: 'Skor 3.5-3.9: Kekuatan karakter yang tinggi',
      color: '#1d4ed8'
    };
    if (score >= 2.5) return {
      level: 'Moderate Strength',
      intensity: 'text-slate-700 font-medium',
      bg: 'bg-slate-50 border-slate-200',
      description: 'Skor 2.5-3.4: Kekuatan karakter yang moderat',
      color: '#475569'
    };
    if (score >= 1.5) return {
      level: 'Lower Strength',
      intensity: 'text-amber-700 font-medium',
      bg: 'bg-amber-50 border-amber-200',
      description: 'Skor 1.5-2.4: Kekuatan karakter yang rendah',
      color: '#d97706'
    };
    return {
      level: 'Development Area',
      intensity: 'text-rose-700 font-medium',
      bg: 'bg-rose-50 border-rose-200',
      description: 'Skor 0.0-1.4: Area yang membutuhkan pengembangan',
      color: '#e11d48'
    };
  };

  // Prepare radar chart data for virtue categories
  const prepareRadarData = (viaIsData) => {
    if (!viaIsData) return [];

    const categoryScores = {};
    Object.entries(viaCategories).forEach(([categoryKey, category]) => {
      const categoryStrengths = category.strengths.filter(strength => viaIsData[strength] !== undefined);
      if (categoryStrengths.length > 0) {
        const avgScore = categoryStrengths.reduce((sum, strength) => sum + (viaIsData[strength] || 0), 0) / categoryStrengths.length;
        categoryScores[categoryKey] = avgScore;
      }
    });

    return Object.entries(categoryScores).map(([key, score]) => ({
      category: viaCategories[key].name,
      value: score,
      fullValue: score
    }));
  };

  // Prepare bar chart data for all strengths
  const prepareBarData = (viaIsData) => {
    if (!viaIsData) return [];

    return Object.entries(viaIsData)
      .map(([key, value]) => ({
        name: strengthLabels[key] || key.replace(/_/g, ' '),
        value: value || 0,
        key: key,
        category: Object.entries(viaCategories).find(([_, cat]) =>
          cat.strengths.includes(key)
        )?.[0] || 'other'
      }))
      .sort((a, b) => b.value - a.value);
  };

  // Custom tooltip for radar chart
  const CustomRadarTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      const categoryKey = Object.keys(viaCategories).find(key => viaCategories[key].name === label);
      const category = viaCategories[categoryKey];

      return (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-white p-4 rounded shadow-lg border border-gray-200 max-w-sm"
        >
          <div className="flex items-center mb-2">
            <span className="text-xl mr-2 text-gray-700">{category?.icon}</span>
            <h4 className="font-semibold text-gray-900">{label}</h4>
          </div>
          <div className="text-lg font-bold mb-2 text-gray-900">
            Average Score: {data.payload.fullValue.toFixed(2)}
          </div>
          <div className="text-xs text-gray-500">
            Includes: {category?.strengths.map(s => strengthLabels[s]).join(', ')}
          </div>
        </motion.div>
      );
    }
    return null;
  };

  // Custom tooltip for bar chart
  const CustomBarTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      const categoryKey = data.payload.category;
      const category = viaCategories[categoryKey];
      const scoreLevel = getScoreLevel(data.value);

      return (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-white p-4 rounded shadow-lg border border-gray-200 max-w-sm"
        >
          <h4 className="font-semibold text-gray-900 mb-2">{label}</h4>
          <div className="flex items-center gap-2 mb-2">
            <div className="text-lg font-bold text-gray-900">
              Score: {data.value.toFixed(2)}
            </div>
            <span className={`text-sm font-medium ${scoreLevel.intensity}`}>
              ({scoreLevel.level})
            </span>
          </div>
          <div className="text-xs text-gray-500 mb-2">
            <strong>Category:</strong> {category?.name} <span className="text-gray-700">{category?.icon}</span>
          </div>
          <p className="text-xs text-gray-600">
            {getStrengthDescription(data.payload.key)}
          </p>
        </motion.div>
      );
    }
    return null;
  };

  // Navigation cards data
  const navigationCards = [
    {
      title: 'Trait Kepribadian',
      subtitle: 'OCEAN Assessment',
      description: 'Pahami dimensi kepribadian utama Anda.',
      path: `/results/${resultId}/ocean`,
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: 'Minat Karier',
      subtitle: 'RIASEC Assessment',
      description: 'Jelajahi minat karier dan preferensi lingkungan kerja Anda.',
      path: `/results/${resultId}/riasec`,
      color: 'from-emerald-500 to-emerald-600'
    },
    {
      title: 'Persona Karier',
      subtitle: 'Integrated Profile',
      description: 'Rekomendasi karier komprehensif berdasarkan profil Anda.',
      path: `/results/${resultId}/persona`,
      color: 'from-indigo-500 to-indigo-600'
    }
  ];

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 overflow-hidden">
        {/* Loading State */}
        {!result && !error && (
          <EnhancedLoadingScreen
            title="Loading VIA Character Strengths..."
            subtitle="Analyzing your character strengths profile"
            skeletonCount={4}
            className="min-h-[600px]"
          />
        )}

        {/* Error State */}
        {error && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="bg-white border border-gray-200 rounded p-6 shadow-sm"
          >
            <div className="flex items-center">
              <div className="text-gray-400 mr-3">⚠️</div>
              <div>
                <h3 className="text-gray-900 font-semibold">Unable to Load Results</h3>
                <p className="text-gray-600 text-sm mt-1">{error}</p>
                <div className="mt-4 space-x-3">
                  <button
                    onClick={() => window.location.reload()}
                    className="bg-gray-900 text-white px-4 py-2 rounded text-sm hover:bg-gray-800 transition-colors"
                  >
                    Retry
                  </button>
                  <button
                    onClick={() => navigate(`/results/${resultId}`)}
                    className="bg-gray-100 text-gray-700 px-4 py-2 rounded text-sm hover:bg-gray-200 transition-colors"
                  >
                    Back to Overview
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Main Content */}
        {result && (
          <>
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-8"
            >
              <div className="flex justify-between items-start mb-6 overflow-hidden">
                <div className="min-w-0 flex-1">
                  <h1 className="text-3xl font-bold text-slate-900 mb-2">
                    VIA Character Strengths Assessment
                  </h1>
                  <p className="text-slate-600 max-w-2xl leading-relaxed">
                    Halaman ini menampilkan hasil penilaian kekuatan karakter Anda berdasarkan VIA (Values in Action) Survey yang merepresentasikan diri anda secara otentik.
                  </p>
                </div>
                <div className="flex space-x-3 flex-shrink-0 ml-4">
                  <button
                    onClick={() => navigate(`/results/${resultId}`)}
                    className="px-4 py-2 border border-slate-300 text-slate-700 rounded-lg hover:bg-slate-50 transition-colors"
                  >
                    ← Back
                  </button>
                  <button
                    onClick={() => navigate('/dashboard')}
                    className="px-4 py-2 bg-slate-900 text-white rounded-lg hover:bg-slate-800 transition-colors"
                  >
                    Dashboard
                  </button>
                </div>
              </div>

              <div className="bg-white rounded-lg p-4 border border-slate-200 shadow-sm">
                <div className="flex items-center justify-between text-sm text-slate-600">
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-slate-900 rounded-sm mr-2"></span>
                    Completed: {formatDate(result.created_at)}
                  </div>
                  <span className="bg-slate-100 text-slate-700 px-3 py-1 rounded-full text-xs font-medium">
                    VIA Character Strengths
                  </span>
                </div>
              </div>
            </motion.div>

            {/* VIA Explanation Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="mb-8"
            >
              <div className="bg-white rounded-lg border border-slate-200 shadow-sm overflow-hidden">
                <div className="bg-gradient-to-r from-slate-50 to-blue-50 border-b border-slate-200 p-6">
                  <div className="flex items-center mb-4">
                    <span className="text-3xl mr-3">💪</span>
                    <div>
                      <h2 className="text-2xl font-bold text-slate-900">{viaExplanation.title}</h2>
                      <p className="text-blue-700 font-semibold">Character Strengths & Virtues</p>
                    </div>
                  </div>
                  <p className="text-slate-700 leading-relaxed mb-4">VIA (Values in Action) Survey merupakan instrumen psikologi positif yang bertujuan mengidentifikasi 24 karakter utama manusia, yang dikelompokkan dalam 6 kategori kebajikan universal. Hasil assessment ini dapat digunakan untuk pengembangan diri, peningkatan kinerja, serta kesejahteraan secara menyeluruh.</p>

                  <div className="grid md:grid-cols-2 gap-4 mb-4">
                    <div className="bg-white p-4 rounded-lg border border-emerald-200">
                    <h4 className="font-semibold text-slate-900 mb-2">👨‍🔬 Pengembang</h4>
                    <p className="text-sm text-slate-700 leading-relaxed">{viaExplanation.developer}</p>
                    </div>
                    <div className="bg-white p-4 rounded-lg border border-blue-200">
                    <h4 className="font-semibold text-slate-900 mb-2">✅ Validitas Ilmiah</h4>
                    <p className="text-sm text-slate-700 leading-relaxed">{viaExplanation.validity}</p>
                    </div>
                  </div>

                  <div className="bg-white p-4 rounded-lg border border-amber-200">
                    <h4 className="font-semibold text-slate-900 mb-2">🎯 Tujuan Assessment</h4>
                    <p className="text-sm text-slate-700 leading-relaxed">Assessment ini bertujuan untuk membantu Anda mengenali kekuatan karakter utama (signature strengths) yang dapat dioptimalkan dalam kehidupan pribadi maupun profesional.</p>
                  </div>
                </div>
              </div>
            </motion.div>



            {/* Virtue Categories Radar Chart */}
            {result.assessment_data?.viaIs && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
                className="mb-8"
              >
                <div className="bg-white rounded-lg border border-slate-200 shadow-sm p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-slate-900 mb-2">Virtue Categories Profile</h3>
                    <p className="text-slate-600 text-sm leading-relaxed">Rata-rata skor Anda pada enam kategori kebajikan utama</p>
                  </div>

                  <div className="h-96">
                    <ResponsiveContainer width="100%" height="100%">
                      <RadarChart data={prepareRadarData(result.assessment_data.viaIs)}>
                        <PolarGrid stroke="#e2e8f0" />
                        <PolarAngleAxis
                          dataKey="category"
                          tick={{ fontSize: 12, fill: '#1e293b', fontWeight: 500 }}
                        />
                        <PolarRadiusAxis
                          angle={90}
                          domain={[0, 5]}
                          tick={{ fontSize: 10, fill: '#64748b' }}
                          tickCount={6}
                        />
                        <Radar
                          name="Virtue Score"
                          dataKey="value"
                          stroke="#1e293b"
                          fill="#3b82f6"
                          fillOpacity={0.1}
                          strokeWidth={2.5}
                        />
                        <Tooltip content={<CustomRadarTooltip />} />
                      </RadarChart>
                    </ResponsiveContainer>
                  </div>

                  <div className="mt-6 grid grid-cols-2 md:grid-cols-3 gap-3">
                    {Object.entries(viaCategories).map(([key, category]) => {
                      const categoryData = prepareRadarData(result.assessment_data.viaIs).find(
                        item => item.category === category.name
                      );
                      const scoreLevel = getScoreLevel(categoryData ? categoryData.fullValue : 0);
                      return (
                        <div key={key} className="text-center p-4 bg-slate-50 rounded-lg border border-slate-200">
                          <div className="text-2xl mb-2" style={{ color: category.color }}>{category.icon}</div>
                          <div className="text-lg font-bold mb-1 text-slate-900">
                            {categoryData ? categoryData.fullValue.toFixed(2) : 'N/A'}
                          </div>
                          <div className="text-xs text-slate-600 font-medium mb-1 truncate">{category.name}</div>
                          <div className={`text-xs px-2 py-1 rounded-full ${scoreLevel.bg} ${scoreLevel.intensity}`}>
                            {scoreLevel.level}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </motion.div>
            )}

            {/* All Strengths Bar Chart */}
            {result.assessment_data?.viaIs && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.6 }}
                className="mb-8"
              >
                <div className="bg-white rounded-lg border border-slate-200 shadow-sm p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-slate-900 mb-2">All 24 Character Strengths</h3>
                    <p className="text-slate-600 text-sm leading-relaxed">Peringkat lengkap kekuatan karakter Anda dari yang tertinggi hingga terendah</p>
                  </div>

                  <div className="h-[500px] overflow-hidden">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={prepareBarData(result.assessment_data.viaIs)}
                        margin={{ top: 20, right: 30, left: 20, bottom: 150 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                        <XAxis
                          dataKey="name"
                          tick={{ fontSize: 8, fill: '#1e293b', fontWeight: 500 }}
                          angle={-45}
                          textAnchor="end"
                          height={150}
                          interval={0}
                        />
                        <YAxis
                          domain={[0, 5]}
                          tick={{ fontSize: 10, fill: '#64748b' }}
                        />
                        <Tooltip content={<CustomBarTooltip />} />
                        <Bar
                          dataKey="value"
                          fill="#1e293b"
                          radius={[3, 3, 0, 0]}
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>


                </div>
              </motion.div>
            )}

            {/* Virtue Categories Detailed Analysis */}
            {result.assessment_data?.viaIs && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.8 }}
                className="mb-8"
              >
                <div className="bg-white rounded-lg border border-slate-200 shadow-sm p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-slate-900 mb-2">Virtue Categories Analysis</h3>
                    <p className="text-slate-600 text-sm leading-relaxed">Analisis mendalam kekuatan karakter Anda berdasarkan kategori kebajikan</p>
                  </div>

                  {/* Virtue Categories Cards - 2 columns 3 rows layout */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {Object.entries(viaCategories).map(([categoryKey, category], index) => {
                      const categoryData = prepareRadarData(result.assessment_data.viaIs).find(
                        item => item.category === category.name
                      );
                      const categoryScore = categoryData ? categoryData.fullValue : 0;
                      const scoreLevel = getScoreLevel(categoryScore);
                      const isHigh = categoryScore >= 3.5;

                      return (
                        <motion.div
                          key={categoryKey}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className="bg-white rounded border border-gray-200 overflow-hidden shadow-sm hover:shadow-md transition-shadow"
                        >
                          {/* Header */}
                          <div className="bg-slate-50 border-b border-slate-200 p-6">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center min-w-0 flex-1">
                                <span className="text-2xl mr-3 flex-shrink-0" style={{ color: category.color }}>{category.icon}</span>
                                <div className="min-w-0 flex-1">
                                  <h3 className="text-xl font-bold text-slate-900 truncate">{category.name}</h3>
                                  <p className="text-sm text-slate-600">{category.strengths.length} Character Strengths</p>
                                </div>
                              </div>
                              <div className="text-right flex-shrink-0 ml-4">
                                <div className="text-2xl font-bold text-slate-900">{categoryScore.toFixed(2)}</div>
                                <div className={`text-sm ${scoreLevel.intensity}`}>{scoreLevel.level}</div>
                              </div>
                            </div>
                          </div>

                          {/* Content */}
                          <div className="p-6">
                            {/* Progress Bar */}
                            <div className="mb-6 overflow-hidden">
                              <div className="bg-slate-200 rounded-full h-3 w-full max-w-full">
                                <motion.div
                                  className="h-3 rounded-full"
                                  style={{ backgroundColor: category.accentColor }}
                                  initial={{ width: 0 }}
                                  animate={{ width: `${Math.min((categoryScore / 5) * 100, 100)}%` }}
                                  transition={{ duration: 1, delay: 0.5 + index * 0.1 }}
                                />
                              </div>
                              <div className="flex justify-between text-xs text-slate-500 mt-1">
                                <span>0.0</span>
                                <span className="font-medium">Signature Strength: 4.0+</span>
                                <span>5.0</span>
                              </div>
                            </div>

                            {/* Strengths in this category */}
                            <div className="mb-6">
                              <h4 className="font-semibold text-slate-900 mb-3">Strengths in this Category:</h4>
                              <div className="space-y-3">
                                {category.strengths.map((strength, idx) => {
                                  const strengthScore = result.assessment_data.viaIs[strength] || 0;
                                  const strengthLabel = strengthLabels[strength] || strength.replace(/_/g, ' ');
                                  const strengthLevel = getScoreLevel(strengthScore);
                                  return (
                                    <div key={idx} className="flex items-center justify-between text-sm p-2 rounded-lg bg-slate-50 border border-slate-200">
                                      <span className="text-slate-700 font-medium flex-1 min-w-0 truncate">{strengthLabel}</span>
                                      <div className="flex items-center gap-2 flex-shrink-0">
                                        <span className="font-bold text-slate-900">{strengthScore.toFixed(2)}</span>
                                        <span className={`text-xs px-2 py-1 rounded-full ${strengthLevel.bg} ${strengthLevel.intensity}`}>
                                          {strengthScore >= 4.0 ? 'Signature' : strengthScore >= 3.5 ? 'High' : strengthScore >= 2.5 ? 'Moderate' : 'Lower'}
                                        </span>
                                      </div>
                                    </div>
                                  );
                                })}
                              </div>
                            </div>

                            {/* Category Implications */}
                            <div className="bg-slate-50 rounded-lg p-4 border border-slate-200">
                              <h4 className="font-semibold text-slate-900 mb-2">Category Overview</h4>
                              <p className="text-sm text-slate-700 leading-relaxed">
                                {isHigh
                                  ? `Skor tinggi pada kategori ${category.name} menunjukkan pengembangan yang kuat pada kekuatan karakter terkait. Anda secara alami merepresentasikan kebajikan dalam kategori ini dan dapat memanfaatkannya untuk mendukung peran profesional maupun kehidupan pribadi.`
                                  : `Skor moderat pada kategori ${category.name} menunjukkan masih terdapat ruang untuk pengembangan kekuatan karakter terkait. Disarankan untuk meningkatkan area ini guna memperkuat profil karakter dan efektivitas diri secara menyeluruh.`
                                }
                              </p>
                            </div>
                          </div>
                        </motion.div>
                      );
                    })}
                  </div>
                </div>
              </motion.div>
            )}

            {/* Main Grid Layout - 2 Columns */}
            {result.assessment_data?.viaIs && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8 overflow-hidden min-w-0">
                {/* Left Column - Signature Strengths */}
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.8 }}
                  className="space-y-6"
                >
                  <div className="bg-white rounded-lg border border-slate-200 shadow-sm p-6 overflow-hidden">
                    <div className="flex items-center mb-6">
                      <div className="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
                        <svg className="w-6 h-6 text-emerald-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <h3 className="text-xl font-bold text-slate-900 min-w-0">Top 5 Signature Strengths</h3>
                    </div>

                    <p className="text-slate-600 mb-6 text-sm leading-relaxed">
                      Lima kekuatan karakter teratas berikut merupakan ciri utama yang paling menonjol dalam diri Anda. Optimalkan kekuatan ini dalam aktivitas sehari-hari maupun pengembangan karier.
                    </p>

                    <div className="space-y-4">
                      {getViaIsInsights(result.assessment_data.viaIs).top.map(([strength, score], idx) => {
                        const scoreLevel = getScoreLevel(score);
                        return (
                          <motion.div
                            key={strength}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.4, delay: 0.9 + idx * 0.1 }}
                            className={`p-4 rounded-lg border ${scoreLevel.bg} overflow-hidden`}
                          >
                            <div className="flex justify-between items-start mb-2">
                              <h4 className="font-semibold text-slate-900 flex-1 mr-2 break-words min-w-0">
                                {strengthLabels[strength] || strength.replace(/_/g, ' ')}
                              </h4>
                              <span className={`text-sm px-3 py-1 rounded-full ${scoreLevel.intensity} bg-white flex-shrink-0 font-bold`}>
                                {score.toFixed(2)}
                              </span>
                            </div>
                            <p className="text-sm text-slate-700 mb-3 leading-relaxed">
                              {getStrengthDescription(strength)}
                            </p>
                            <div className="bg-slate-200 rounded-full h-2.5 overflow-hidden">
                              <motion.div
                                className="bg-emerald-500 h-2.5 rounded-full"
                                initial={{ width: 0 }}
                                animate={{ width: `${Math.min((score / 5) * 100, 100)}%` }}
                                transition={{ duration: 0.8, delay: 1.0 + idx * 0.1 }}
                              />
                            </div>
                          </motion.div>
                        );
                      })}
                    </div>
                  </div>
                </motion.div>

                {/* Right Column - Development Areas */}
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.8 }}
                  className="space-y-6"
                >
                  <div className="bg-white rounded-lg border border-slate-200 shadow-sm p-6 overflow-hidden">
                    <div className="flex items-center mb-6">
                      <div className="w-10 h-10 bg-amber-100 rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
                        <svg className="w-6 h-6 text-amber-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <h3 className="text-xl font-bold text-slate-900 min-w-0">Development Opportunities</h3>
                    </div>

                    <p className="text-slate-600 mb-6 text-sm leading-relaxed">
                      Kekuatan karakter berikut memiliki skor lebih rendah, namun tetap dapat dikembangkan sesuai kebutuhan dan tujuan Anda. Pertimbangkan untuk memperkuat area ini demi mendukung pertumbuhan pribadi secara optimal.
                    </p>

                    <div className="space-y-4">
                      {getViaIsInsights(result.assessment_data.viaIs).bottom.map(([strength, score], idx) => {
                        const scoreLevel = getScoreLevel(score);
                        return (
                          <motion.div
                            key={strength}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.4, delay: 0.9 + idx * 0.1 }}
                            className={`p-4 rounded-lg border ${scoreLevel.bg} overflow-hidden`}
                          >
                            <div className="flex justify-between items-start mb-2">
                              <h4 className="font-semibold text-slate-900 flex-1 mr-2 break-words min-w-0">
                                {strengthLabels[strength] || strength.replace(/_/g, ' ')}
                              </h4>
                              <span className={`text-sm px-3 py-1 rounded-full ${scoreLevel.intensity} bg-white flex-shrink-0 font-bold`}>
                                {score.toFixed(2)}
                              </span>
                            </div>
                            <p className="text-sm text-slate-700 mb-3 leading-relaxed">
                              {getStrengthDescription(strength)}
                            </p>
                            <div className="bg-slate-200 rounded-full h-2.5 overflow-hidden">
                              <motion.div
                                className="bg-amber-500 h-2.5 rounded-full"
                                initial={{ width: 0 }}
                                animate={{ width: `${Math.min((score / 5) * 100, 100)}%` }}
                                transition={{ duration: 0.8, delay: 1.0 + idx * 0.1 }}
                              />
                            </div>
                          </motion.div>
                        );
                      })}
                    </div>
                  </div>
                </motion.div>
              </div>
            )}

            {/* Navigation to Other Results */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 1.4 }}
              className="mb-12"
            >
              <div className="bg-gradient-to-r from-slate-50 to-blue-50 rounded-lg p-8 mb-8">
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-lg mb-4">
                    <span className="text-2xl">🧭</span>
                  </div>
                  <h2 className="text-3xl font-bold text-slate-900 mb-3">
                    Jelajahi Profil Lengkap Anda
                  </h2>
                  <p className="text-slate-600 text-lg max-w-2xl mx-auto leading-relaxed">
                    Lanjutkan eksplorasi untuk memahami berbagai aspek kepribadian dan potensi karier Anda secara komprehensif. Setiap assessment memberikan wawasan yang saling melengkapi untuk pengembangan diri yang berkelanjutan.
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
                {navigationCards.map((card, index) => (
                  <motion.div
                    key={card.title}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 1.5 + index * 0.1 }}
                    whileHover={{
                      y: -4,
                      transition: { duration: 0.15 }
                    }}
                    className="group cursor-pointer"
                    onClick={() => navigate(card.path)}
                  >
                    <div className="bg-white rounded-lg p-6 shadow-sm border border-slate-200 hover:shadow-xl hover:border-slate-300 transition-all duration-300 h-full">
                      <div className="flex flex-col h-full">
                        <div className="flex items-start justify-end mb-4">
                          <motion.svg
                            className="w-5 h-5 text-slate-400 group-hover:text-slate-600 transition-colors"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            whileHover={{ x: 3 }}
                            transition={{ duration: 0.2 }}
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </motion.svg>
                        </div>

                        <div className="flex-grow">
                          <h3 className="text-xl font-bold text-slate-900 mb-2 group-hover:text-slate-700 transition-colors">
                            {card.title}
                          </h3>
                          <p className="text-sm text-slate-500 mb-3 font-semibold uppercase tracking-wide">
                            {card.subtitle}
                          </p>
                          <p className="text-slate-600 leading-relaxed">
                            {card.description}
                          </p>
                        </div>

                        <div className="mt-4 pt-4 border-t border-slate-200">
                          <div className="flex items-center text-sm font-medium text-slate-500 group-hover:text-blue-600 transition-colors">
                            <span>Lihat Assessment</span>
                            <motion.svg
                              className="w-4 h-4 ml-1"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                              whileHover={{ x: 2 }}
                              transition={{ duration: 0.2 }}
                            >
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                            </motion.svg>
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

          </>
        )}
      </div>
    </div>
  );
};

export default ResultViaIs;